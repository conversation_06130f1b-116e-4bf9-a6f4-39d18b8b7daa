package com.geeksec.knowledgebase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 威胁检查服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThreatService {

    /**
     * C2威胁域名集合
     */
    private final Set<String> c2ThreatDomains = ConcurrentHashMap.newKeySet();

    /**
     * C2威胁IP集合
     */
    private final Set<String> c2ThreatIps = ConcurrentHashMap.newKeySet();

    /**
     * IOC IP集合
     */
    private final Set<String> iocIps = ConcurrentHashMap.newKeySet();

    /**
     * IOC域名集合
     */
    private final Set<String> iocDomains = ConcurrentHashMap.newKeySet();

    @PostConstruct
    public void init() {
        log.info("初始化威胁检查服务");
        loadC2ThreatDomains();
        loadC2ThreatIps();
        loadIocIps();
        loadIocDomains();
        log.info("威胁检查服务初始化完成，C2威胁域名: {} 个，C2威胁IP: {} 个，IOC IP: {} 个，IOC域名: {} 个",
                c2ThreatDomains.size(), c2ThreatIps.size(), iocIps.size(), iocDomains.size());
    }

    /**
     * 检查域名是否为C2威胁域名
     *
     * @param domain 域名
     * @return 是否为C2威胁域名
     */
    @Cacheable(value = "threat-c2-domain", key = "#domain")
    public boolean isC2ThreatDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        String normalizedDomain = domain.toLowerCase().trim();
        return c2ThreatDomains.contains(normalizedDomain) || 
               c2ThreatDomains.stream().anyMatch(c2Domain -> 
                   normalizedDomain.endsWith("." + c2Domain) || c2Domain.endsWith("." + normalizedDomain));
    }

    /**
     * 检查IP是否为C2威胁IP
     *
     * @param ip IP地址
     * @return 是否为C2威胁IP
     */
    @Cacheable(value = "threat-c2-ip", key = "#ip")
    public boolean isC2ThreatIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        return c2ThreatIps.contains(ip.trim());
    }

    /**
     * 检查IP是否为IOC IP
     *
     * @param ip IP地址
     * @return 是否为IOC IP
     */
    @Cacheable(value = "threat-ioc-ip", key = "#ip")
    public boolean isIocIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        return iocIps.contains(ip.trim());
    }

    /**
     * 检查域名是否为IOC域名
     *
     * @param domain 域名
     * @return 是否为IOC域名
     */
    @Cacheable(value = "threat-ioc-domain", key = "#domain")
    public boolean isIocDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        String normalizedDomain = domain.toLowerCase().trim();
        return iocDomains.contains(normalizedDomain);
    }

    /**
     * 综合检查域名威胁
     *
     * @param domain 域名
     * @return 威胁检查结果
     */
    public Map<String, Object> checkDomainThreats(String domain) {
        Map<String, Object> result = new HashMap<>();
        result.put("domain", domain);
        result.put("isC2Threat", isC2ThreatDomain(domain));
        result.put("isIoc", isIocDomain(domain));
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 综合检查IP威胁
     *
     * @param ip IP地址
     * @return 威胁检查结果
     */
    public Map<String, Object> checkIpThreats(String ip) {
        Map<String, Object> result = new HashMap<>();
        result.put("ip", ip);
        result.put("isC2Threat", isC2ThreatIp(ip));
        result.put("isIoc", isIocIp(ip));
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 加载C2威胁域名
     */
    private void loadC2ThreatDomains() {
        // 添加一些示例C2威胁域名
        c2ThreatDomains.addAll(Set.of(
                "c2-server.example.com",
                "botnet-command.net",
                "malware-control.org",
                "apt-c2.info"
        ));
        
        // 从资源文件加载
        loadResourceFile("/threat/c2_threat_domains.txt", c2ThreatDomains, "C2威胁域名");
        
        log.info("成功加载C2威胁域名 {} 个", c2ThreatDomains.size());
    }

    /**
     * 加载C2威胁IP
     */
    private void loadC2ThreatIps() {
        // 添加一些示例C2威胁IP
        c2ThreatIps.addAll(Set.of(
                "*************",
                "*********",
                "***********",
                "************"
        ));
        
        // 从资源文件加载
        loadResourceFile("/threat/c2_threat_ips.txt", c2ThreatIps, "C2威胁IP");
        
        log.info("成功加载C2威胁IP {} 个", c2ThreatIps.size());
    }

    /**
     * 加载IOC IP
     */
    private void loadIocIps() {
        // 添加一些示例IOC IP
        iocIps.addAll(Set.of(
                "*************",
                "************",
                "**********",
                "*************"
        ));
        
        // 从资源文件加载
        loadResourceFile("/threat/ioc_ips.txt", iocIps, "IOC IP");
        
        log.info("成功加载IOC IP {} 个", iocIps.size());
    }

    /**
     * 加载IOC域名
     */
    private void loadIocDomains() {
        // 添加一些示例IOC域名
        iocDomains.addAll(Set.of(
                "malicious-ioc.example.com",
                "threat-indicator.net",
                "suspicious-activity.org",
                "compromise-indicator.info"
        ));
        
        // 从资源文件加载
        loadResourceFile("/threat/ioc_domains.txt", iocDomains, "IOC域名");
        
        log.info("成功加载IOC域名 {} 个", iocDomains.size());
    }

    /**
     * 从资源文件加载数据到集合
     */
    private void loadResourceFile(String resourcePath, Set<String> targetSet, String description) {
        try (InputStream is = getClass().getResourceAsStream(resourcePath)) {
            if (is == null) {
                log.warn("{}文件未找到: {}", description, resourcePath);
                return;
            }
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                int count = 0;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        targetSet.add(line.toLowerCase());
                        count++;
                    }
                }
                log.info("从文件 {} 加载 {} {} 个", resourcePath, description, count);
            }
        } catch (IOException e) {
            log.error("加载{}文件失败: {}", description, resourcePath, e);
        }
    }
}
