package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.ThreatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 威胁检查控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/threat")
@RequiredArgsConstructor
@Tag(name = "Threat Controller", description = "威胁检查相关API")
public class ThreatController {

    private final ThreatService threatService;

    // ==================== C2威胁检查 ====================

    @GetMapping("/c2/domain/check/{domain}")
    @Operation(summary = "检查域名是否为C2威胁域名")
    public ApiResponse<Map<String, Object>> checkC2ThreatDomain(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查C2威胁域名: {}", domain);
        boolean isC2Threat = threatService.isC2ThreatDomain(domain);
        Map<String, Object> response = Map.of(
                "domain", domain,
                "isC2Threat", isC2Threat
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/c2/ip/check/{ip}")
    @Operation(summary = "检查IP是否为C2威胁IP")
    public ApiResponse<Map<String, Object>> checkC2ThreatIp(
            @Parameter(description = "IP地址") @PathVariable String ip) {
        log.debug("检查C2威胁IP: {}", ip);
        boolean isC2Threat = threatService.isC2ThreatIp(ip);
        Map<String, Object> response = Map.of(
                "ip", ip,
                "isC2Threat", isC2Threat
        );
        return ApiResponse.success(response);
    }

    // ==================== IOC威胁检查 ====================

    @GetMapping("/ioc/ip/check/{ip}")
    @Operation(summary = "检查IP是否为IOC IP")
    public ApiResponse<Map<String, Object>> checkIocIp(
            @Parameter(description = "IP地址") @PathVariable String ip) {
        log.debug("检查IOC IP: {}", ip);
        boolean isIoc = threatService.isIocIp(ip);
        Map<String, Object> response = Map.of(
                "ip", ip,
                "isIoc", isIoc
        );
        return ApiResponse.success(response);
    }

    // ==================== 通用威胁检查 ====================

    @GetMapping("/check/domain/{domain}")
    @Operation(summary = "综合检查域名威胁")
    public ApiResponse<Map<String, Object>> checkDomainThreats(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("综合检查域名威胁: {}", domain);
        Map<String, Object> threats = threatService.checkDomainThreats(domain);
        return ApiResponse.success(threats);
    }

    @GetMapping("/check/ip/{ip}")
    @Operation(summary = "综合检查IP威胁")
    public ApiResponse<Map<String, Object>> checkIpThreats(
            @Parameter(description = "IP地址") @PathVariable String ip) {
        log.debug("综合检查IP威胁: {}", ip);
        Map<String, Object> threats = threatService.checkIpThreats(ip);
        return ApiResponse.success(threats);
    }
}
