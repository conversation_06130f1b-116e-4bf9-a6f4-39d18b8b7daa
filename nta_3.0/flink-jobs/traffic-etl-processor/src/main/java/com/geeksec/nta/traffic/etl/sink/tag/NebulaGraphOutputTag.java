package com.geeksec.nta.traffic.etl.sink.tag;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Nebula Graph输出标签
 * 定义了所有用于Nebula Graph的输出标签
 *
 * <AUTHOR> Team
 */
public class NebulaGraphOutputTag {


    /**
     * 顶点输出标签
     * 用于路由顶点数据到Nebula Graph
     */
    public static final class Vertex {
        /**
         * IP顶点
         */
        public static final OutputTag<Row> IP = new OutputTag<>("nebula_vertex_ip", TypeInformation.of(Row.class));

        /**
         * 设备顶点
         */
        public static final OutputTag<Row> DEVICE = new OutputTag<>("nebula_vertex_device", TypeInformation.of(Row.class));

        /**
         * 应用顶点
         */
        public static final OutputTag<Row> APPLICATION = new OutputTag<>("nebula_vertex_application", TypeInformation.of(Row.class));

        /**
         * 域名顶点
         */
        public static final OutputTag<Row> DOMAIN = new OutputTag<>("nebula_vertex_domain", TypeInformation.of(Row.class));

        /**
         * URL顶点
         */
        public static final OutputTag<Row> URL = new OutputTag<>("nebula_vertex_url", TypeInformation.of(Row.class));

        /**
         * 证书顶点
         */
        public static final OutputTag<Row> CERTIFICATE = new OutputTag<>("nebula_vertex_certificate", TypeInformation.of(Row.class));

        /**
         * 证书颁发者顶点
         */
        public static final OutputTag<Row> ISSUER = new OutputTag<>("nebula_vertex_issuer", TypeInformation.of(Row.class));

        /**
         * 证书主体顶点
         */
        public static final OutputTag<Row> SUBJECT = new OutputTag<>("nebula_vertex_subject", TypeInformation.of(Row.class));

        /**
         * 组织顶点
         */
        public static final OutputTag<Row> ORGANIZATION = new OutputTag<>("nebula_vertex_organization", TypeInformation.of(Row.class));

        /**
         * 可注册域名顶点
         */
        public static final OutputTag<Row> REGISTRABLE_DOMAIN = new OutputTag<>("nebula_vertex_registrable_domain", TypeInformation.of(Row.class));
    }

    /**
     * 边输出标签
     * 用于路由边数据到Nebula Graph
     */
    public static final class Edge {
        // ==================== 边类型定义 ====================
        // 网络层关系（IP、MAC）
        // IP与MAC地址映射关系
        public static final OutputTag<Row> IP_MAPS_TO_MAC_TAG = new OutputTag<>("ip_maps_to_mac", TypeInformation.of(Row.class));
        // 链路层连接
        public static final OutputTag<Row> MAC_CONNECTS_TO_MAC_TAG = new OutputTag<>("mac_connects_to_mac", TypeInformation.of(Row.class));
        // 网络层连接
        public static final OutputTag<Row> IP_CONNECTS_TO_IP_TAG = new OutputTag<>("ip_connects_to_ip", TypeInformation.of(Row.class));
        // 攻击者攻击目标
        public static final OutputTag<Row> ATTACKS_TAG = new OutputTag<>("attacks", TypeInformation.of(Row.class));

        // 域名派生自可注册域，is_sni表示是否为TLS会话中的SNI域名
        public static final OutputTag<Row> DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN_TAG = new OutputTag<>("domain_derives_from_registrable_domain", TypeInformation.of(Row.class));
        // CNAME指向目标域名
        public static final OutputTag<Row> CNAME_POINTS_TO_DOMAIN_TAG = new OutputTag<>("cname_points_to_domain", TypeInformation.of(Row.class));

        // DNS解析关系
        // DNS查询域名
        public static final OutputTag<Row> CLIENT_QUERIES_DOMAIN_TAG = new OutputTag<>("client_queries_domain", TypeInformation.of(Row.class));
        // 向DNS服务器查询
        public static final OutputTag<Row> CLIENT_QUERIES_DNS_SERVER_TAG = new OutputTag<>("client_queries_dns_server", TypeInformation.of(Row.class));
        // DNS服务器解析域名
        public static final OutputTag<Row> DNS_SERVER_RESOLVES_DOMAIN_TAG = new OutputTag<>("dns_server_resolves_domain", TypeInformation.of(Row.class));
        // 域名解析到IP，record_type可为'A'或'AAAA'等
        public static final OutputTag<Row> DOMAIN_RESOLVES_TO_IP_TAG = new OutputTag<>("domain_resolves_to_ip", TypeInformation.of(Row.class));

        // HTTP/TLS会话关系
        // HTTP客户端访问域名
        public static final OutputTag<Row> CLIENT_HTTP_REQUESTS_DOMAIN_TAG = new OutputTag<>("client_http_requests_domain", TypeInformation.of(Row.class));
        // HTTP服务端提供域名
        public static final OutputTag<Row> SERVER_HTTP_SERVES_DOMAIN_TAG = new OutputTag<>("server_http_serves_domain", TypeInformation.of(Row.class));
        // TLS客户端访问域名
        public static final OutputTag<Row> CLIENT_TLS_REQUESTS_DOMAIN_TAG = new OutputTag<>("client_tls_requests_domain", TypeInformation.of(Row.class));
        // TLS服务端托管域名
        public static final OutputTag<Row> SERVER_TLS_HOSTS_DOMAIN_TAG = new OutputTag<>("server_tls_hosts_domain", TypeInformation.of(Row.class));

        // 证书相关关系
        // IP提供证书，role可为'client'或'server'，表示在网络会话中观察到的IP与证书的关系
        public static final OutputTag<Row> IP_PROVIDES_CERT_TAG = new OutputTag<>("ip_provides_cert", TypeInformation.of(Row.class));
        // 客户端在TLS会话中接收/验证服务端的证书
        public static final OutputTag<Row> CLIENT_RECEIVES_CERT_TAG = new OutputTag<>("client_receives_cert", TypeInformation.of(Row.class));
        // 证书包含颁发者信息
        public static final OutputTag<Row> CERT_HAS_ISSUER_TAG = new OutputTag<>("cert_has_issuer", TypeInformation.of(Row.class));
        // 证书包含主体信息
        public static final OutputTag<Row> CERT_HAS_SUBJECT_TAG = new OutputTag<>("cert_has_subject", TypeInformation.of(Row.class));
        // CA证书签发其他证书
        public static final OutputTag<Row> CERT_SIGNS_CERT_TAG = new OutputTag<>("cert_signs_cert", TypeInformation.of(Row.class));
        // 证书覆盖特定域名
        public static final OutputTag<Row> CERT_COVERS_DOMAIN_TAG = new OutputTag<>("cert_covers_domain", TypeInformation.of(Row.class));
        // 证书覆盖可注册域
        public static final OutputTag<Row> CERT_COVERS_REGISTRABLE_DOMAIN_TAG = new OutputTag<>("cert_covers_registrable_domain", TypeInformation.of(Row.class));
        // 证书用于服务特定SNI域名
        public static final OutputTag<Row> CERT_SERVES_SNI_TAG = new OutputTag<>("cert_serves_sni", TypeInformation.of(Row.class));
        // 证书为URL连接提供安全保障
        public static final OutputTag<Row> CERT_SECURES_CONNECTION_TO_URL_TAG = new OutputTag<>("cert_secures_connection_to_url", TypeInformation.of(Row.class));

        // TLS指纹关系
        // IP呈现特定TLS指纹特征，role可为client或server
        public static final OutputTag<Row> IP_PRESENTS_FINGERPRINT_TAG = new OutputTag<>("ip_presents_fingerprint", TypeInformation.of(Row.class));
        // TLS指纹与特定证书在同一TLS会话中出现
        public static final OutputTag<Row> FINGERPRINT_APPEARS_WITH_CERT_TAG = new OutputTag<>("fingerprint_appears_with_cert", TypeInformation.of(Row.class));
        // TLS指纹与特定域名在同一TLS会话中出现
        public static final OutputTag<Row> FINGERPRINT_APPEARS_WITH_DOMAIN_TAG = new OutputTag<>("fingerprint_appears_with_domain", TypeInformation.of(Row.class));
        // TLS指纹识别特定应用程序
        public static final OutputTag<Row> FINGERPRINT_IDENTIFIES_APP_TAG = new OutputTag<>("fingerprint_identifies_app", TypeInformation.of(Row.class));

        // 应用服务关系
        // 客户端访问应用服务
        public static final OutputTag<Row> CLIENT_ACCESSES_APP_TAG = new OutputTag<>("client_accesses_app", TypeInformation.of(Row.class));
        // 应用部署在服务器
        public static final OutputTag<Row> APP_DEPLOYS_ON_SERVER_TAG = new OutputTag<>("app_deploys_on_server", TypeInformation.of(Row.class));
        // 应用程序使用证书
        public static final OutputTag<Row> APP_USES_CERT_TAG = new OutputTag<>("app_uses_cert", TypeInformation.of(Row.class));
        // 应用程序使用域名
        public static final OutputTag<Row> APP_USES_DOMAIN_TAG = new OutputTag<>("app_uses_domain", TypeInformation.of(Row.class));
        // 应用程序使用IP地址
        public static final OutputTag<Row> APP_USES_IP_TAG = new OutputTag<>("app_uses_ip", TypeInformation.of(Row.class));

        // UA/设备/OS关系
        // 客户端UA标识
        public static final OutputTag<Row> CLIENT_USES_UA_TAG = new OutputTag<>("client_uses_ua", TypeInformation.of(Row.class));
        // UA请求域名
        public static final OutputTag<Row> UA_REQUESTS_DOMAIN_TAG = new OutputTag<>("ua_requests_domain", TypeInformation.of(Row.class));
        // UA包含设备信息
        public static final OutputTag<Row> UA_HAS_DEVICE_TAG = new OutputTag<>("ua_has_device", TypeInformation.of(Row.class));
        // UA包含系统信息
        public static final OutputTag<Row> UA_HAS_OS_TAG = new OutputTag<>("ua_has_os", TypeInformation.of(Row.class));
        // UA包含应用信息
        public static final OutputTag<Row> UA_HAS_APP_TAG = new OutputTag<>("ua_has_app", TypeInformation.of(Row.class));

        // 组织所有权关系
        // 组织拥有域名
        public static final OutputTag<Row> ORG_OWNS_DOMAIN_TAG = new OutputTag<>("org_owns_domain", TypeInformation.of(Row.class));
        // 组织拥有可注册域
        public static final OutputTag<Row> ORG_OWNS_REGISTRABLE_DOMAIN_TAG = new OutputTag<>("org_owns_registrable_domain", TypeInformation.of(Row.class));
        // 组织拥有IP
        public static final OutputTag<Row> ORG_OWNS_IP_TAG = new OutputTag<>("org_owns_ip", TypeInformation.of(Row.class));
        // 组织拥有证书
        public static final OutputTag<Row> ORG_OWNS_CERT_TAG = new OutputTag<>("org_owns_cert", TypeInformation.of(Row.class));

        // URL相关关系
        // URL包含域名
        public static final OutputTag<Row> URL_CONTAINS_DOMAIN_TAG = new OutputTag<>("url_contains_domain", TypeInformation.of(Row.class));
        // IP提供URL服务
        public static final OutputTag<Row> IP_PROVIDES_URL_SERVICE_TAG = new OutputTag<>("ip_provides_url_service", TypeInformation.of(Row.class));
    }
}
